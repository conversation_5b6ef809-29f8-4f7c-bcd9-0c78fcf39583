import { apiClient } from './api';
import { UploadedFile, UploadOptions, PaginatedResponse } from '@/types';

export interface ListOptions {
  page?: number;
  limit?: number;
  type?: string;
  source?: string;
  search?: string;
}

export interface BatchOptions {
  [key: string]: any;
}

export interface UploadStats {
  totalFiles: number;
  processedFiles: number;
  knowledgeBaseFiles: number;
  processingRate: number;
  knowledgeBaseRate: number;
  sizeStats: {
    totalSize: number;
    avgSize: number;
    maxSize: number;
    minSize: number;
  };
  typeStats: Array<{
    _id: string;
    count: number;
    totalSize: number;
  }>;
  timestamp: string;
}

class UploadService {
  // Upload single file
  async uploadFile(file: File, options: UploadOptions = {}): Promise<{
    id: string;
    originalName: string;
    fileName: string;
    mimeType: string;
    size: number;
    extractedText?: string;
    addedToKnowledgeBase: boolean;
    timestamp: string;
  }> {
    const formData = new FormData();
    formData.append('file', file);
    
    // Add options as form data
    Object.entries(options).forEach(([key, value]) => {
      if (value !== undefined) {
        formData.append(key, String(value));
      }
    });

    return apiClient.upload('/upload/file', formData);
  }

  // Upload multiple files
  async uploadFiles(files: File[], options: UploadOptions = {}): Promise<{
    successful: Array<{
      id: string;
      originalName: string;
      fileName: string;
      mimeType: string;
      size: number;
      extractedText?: string;
      addedToKnowledgeBase: boolean;
      timestamp: string;
    }>;
    errors: Array<{
      fileName: string;
      error: string;
    }>;
    totalFiles: number;
    successCount: number;
    errorCount: number;
    timestamp: string;
  }> {
    const formData = new FormData();
    
    // Add all files
    files.forEach(file => {
      formData.append('files', file);
    });
    
    // Add options as form data
    Object.entries(options).forEach(([key, value]) => {
      if (value !== undefined) {
        formData.append(key, String(value));
      }
    });

    return apiClient.upload('/upload/files', formData);
  }

  // Upload from URL
  async uploadFromUrl(url: string, options: UploadOptions = {}): Promise<{
    id: string;
    originalName: string;
    url: string;
    mimeType: string;
    size: number;
    extractedText?: string;
    addedToKnowledgeBase: boolean;
    timestamp: string;
  }> {
    return apiClient.post('/upload/url', {
      url,
      ...options,
    });
  }

  // Get uploaded file details
  async getFile(id: string): Promise<UploadedFile> {
    return apiClient.get(`/upload/files/${id}`);
  }

  // Get file content (extracted text)
  async getFileContent(id: string): Promise<{
    id: string;
    originalName: string;
    extractedText?: string;
    mimeType: string;
    size: number;
    timestamp: string;
  }> {
    return apiClient.get(`/upload/files/${id}/content`);
  }

  // List uploaded files
  async listFiles(options: ListOptions = {}): Promise<{
    files: UploadedFile[];
    pagination: {
      page: number;
      limit: number;
      totalCount: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
    timestamp: string;
  }> {
    return apiClient.get('/upload/files', { params: options });
  }

  // Delete file
  async deleteFile(id: string): Promise<void> {
    return apiClient.delete(`/upload/files/${id}`);
  }

  // Batch process files
  async batchProcess(
    fileIds: string[], 
    operation: string, 
    options: BatchOptions = {}
  ): Promise<{
    operation: string;
    successful: any[];
    errors: Array<{
      fileId: string;
      error: string;
    }>;
    totalFiles: number;
    successCount: number;
    errorCount: number;
    timestamp: string;
  }> {
    return apiClient.post('/upload/batch', {
      fileIds,
      operation,
      options,
    });
  }

  // Get upload statistics
  async getStats(): Promise<UploadStats> {
    return apiClient.get('/upload/stats');
  }

  // Download file
  async downloadFile(id: string, filename?: string): Promise<void> {
    return apiClient.download(`/upload/files/${id}/download`, filename);
  }

  // Extract text from existing file
  async extractText(id: string): Promise<{
    id: string;
    extractedText: string;
    alreadyExtracted: boolean;
  }> {
    return apiClient.post(`/upload/files/${id}/extract`);
  }

  // Add file to knowledge base
  async addToKnowledgeBase(id: string, options: any = {}): Promise<{
    id: string;
    addedToKnowledgeBase: boolean;
    alreadyAdded: boolean;
  }> {
    return apiClient.post(`/upload/files/${id}/knowledge-base`, options);
  }

  // Search uploaded files
  async searchFiles(
    query: string, 
    options: Omit<ListOptions, 'search'> = {}
  ): Promise<{
    files: UploadedFile[];
    pagination: {
      page: number;
      limit: number;
      totalCount: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
    timestamp: string;
  }> {
    return apiClient.get('/upload/search', {
      params: { q: query, ...options }
    });
  }

  // Get file types
  async getFileTypes(): Promise<Array<{
    mimeType: string;
    extension: string;
    count: number;
    totalSize: number;
  }>> {
    return apiClient.get('/upload/types');
  }

  // Get upload history
  async getUploadHistory(
    startDate?: string,
    endDate?: string,
    limit: number = 100
  ): Promise<Array<{
    date: string;
    count: number;
    totalSize: number;
    successRate: number;
  }>> {
    const params: any = { limit };
    if (startDate) params.startDate = startDate;
    if (endDate) params.endDate = endDate;

    return apiClient.get('/upload/history', { params });
  }

  // Validate file before upload
  async validateFile(file: File): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
    estimatedProcessingTime?: number;
  }> {
    const formData = new FormData();
    formData.append('file', file);

    return apiClient.upload('/upload/validate', formData);
  }

  // Get supported file types
  async getSupportedTypes(): Promise<{
    mimeTypes: string[];
    extensions: string[];
    maxSize: number;
    description: string;
  }> {
    return apiClient.get('/upload/supported-types');
  }

  // Bulk upload from URLs
  async uploadFromUrls(
    urls: string[], 
    options: UploadOptions = {}
  ): Promise<{
    successful: any[];
    errors: Array<{
      url: string;
      error: string;
    }>;
    totalUrls: number;
    successCount: number;
    errorCount: number;
    timestamp: string;
  }> {
    return apiClient.post('/upload/urls', {
      urls,
      ...options,
    });
  }

  // Get processing status
  async getProcessingStatus(id: string): Promise<{
    id: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress: number;
    message?: string;
    estimatedTimeRemaining?: number;
  }> {
    return apiClient.get(`/upload/files/${id}/status`);
  }

  // Cancel processing
  async cancelProcessing(id: string): Promise<void> {
    return apiClient.post(`/upload/files/${id}/cancel`);
  }

  // Retry failed processing
  async retryProcessing(id: string): Promise<{
    id: string;
    status: string;
    message: string;
  }> {
    return apiClient.post(`/upload/files/${id}/retry`);
  }

  // Export file list
  async exportFileList(
    format: 'json' | 'csv' | 'xlsx' = 'json',
    filters: ListOptions = {}
  ): Promise<Blob> {
    const params: Record<string, string> = {
      format,
    };

    // Convert filters to string values for URLSearchParams
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params[key] = String(value);
      }
    });

    const response = await fetch(`${apiClient.baseURL}/upload/export?${new URLSearchParams(params)}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
      },
    });

    if (!response.ok) {
      throw new Error('Export failed');
    }

    return response.blob();
  }
}

// Create and export service instance
export const uploadService = new UploadService();
export default uploadService;
