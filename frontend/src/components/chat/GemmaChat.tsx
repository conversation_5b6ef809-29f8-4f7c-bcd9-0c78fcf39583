import React, { useState, useRef, useEffect } from 'react';
import Card from '@/components/atoms/Card';
import { <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from '../../components/ui/card';
import Button from '@/components/atoms/Button';
import { Textarea } from '../../components/ui/textarea';
import Badge from '@/components/atoms/Badge';
import { Send, Bot, User, Loader2, Brain } from 'lucide-react';
import { useAGUIClient } from '../../hooks/useAGUIClient';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  context?: any;
}

interface GemmaChatProps {
  onSendMessage: (message: string) => void;
  context?: {
    currentSupplement?: string | null;
    graphData?: any;
    insights?: any;
  };
  disabled?: boolean;
}

export const GemmaChat: React.FC<GemmaChatProps> = ({
  onSendMessage,
  context,
  disabled = false
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  const { lastMessage } = useAGUIClient();

  // Predefined questions for quick access
  const quickQuestions = [
    "What are the main benefits of this supplement?",
    "Are there any side effects or warnings?",
    "How does this supplement work in the body?",
    "What health domains does this supplement affect?",
    "Are there any drug interactions to be aware of?",
    "What's the recommended dosage?",
    "Show me similar supplements in the graph"
  ];

  // Handle incoming responses from AG-UI
  useEffect(() => {
    if (lastMessage?.type === 'query_response') {
      const { query, response } = lastMessage;
      
      // Add user message if not already added
      setMessages(prev => {
        const hasUserMessage = prev.some(msg => 
          msg.type === 'user' && msg.content === query
        );
        
        const newMessages = hasUserMessage ? prev : [
          ...prev,
          {
            id: Date.now().toString(),
            type: 'user',
            content: query ?? '',
            timestamp: new Date()
          } as ChatMessage
        ];
        
        return [
          ...newMessages,
          {
            id: (Date.now() + 1).toString(),
            type: 'assistant',
            content: response ?? '',
            timestamp: new Date(),
            context
          } as ChatMessage
        ];
      });
      
      setIsLoading(false);
    }
  }, [lastMessage, context]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || disabled || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);
    
    // Send message through AG-UI
    onSendMessage(inputMessage.trim());
    
    setInputMessage('');
    
    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }
  };

  const handleQuickQuestion = (question: string) => {
    setInputMessage(question);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatMessage = (content: string) => {
    // Simple markdown-like formatting
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/\n/g, '<br />');
  };

  const getContextInfo = () => {
    if (!context?.currentSupplement) return null;
    
    const info = [`Currently researching: ${context.currentSupplement}`];
    
    if (context.graphData?.nodes) {
      info.push(`Graph contains ${context.graphData.nodes.length} nodes`);
    }
    
    return info.join(' • ');
  };

  return (
    <Card className="h-[600px] flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            Gemma AI Assistant
          </CardTitle>
          {context?.currentSupplement && (
            <Badge variant="secondary">
              {context.currentSupplement}
            </Badge>
          )}
        </div>
        {getContextInfo() && (
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {getContextInfo()}
          </p>
        )}
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col space-y-4">
        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto space-y-4 pr-2">
          {messages.length === 0 && (
            <div className="text-center text-gray-500 py-8">
              <Bot className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <p className="text-lg font-medium mb-2">Welcome to Gemma AI Assistant!</p>
              <p className="text-sm">
                Ask me anything about supplements, their effects, or the knowledge graph.
              </p>
            </div>
          )}
          
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${
                message.type === 'user' ? 'justify-end' : 'justify-start'
              }`}
            >
              {message.type === 'assistant' && (
                <div className="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center flex-shrink-0">
                  <Bot className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                </div>
              )}
              
              <div
                className={`max-w-[80%] rounded-lg px-4 py-2 ${
                  message.type === 'user'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100'
                }`}
              >
                <div
                  dangerouslySetInnerHTML={{
                    __html: formatMessage(message.content)
                  }}
                />
                <div className="text-xs opacity-70 mt-1">
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>
              
              {message.type === 'user' && (
                <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center flex-shrink-0">
                  <User className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </div>
              )}
            </div>
          ))}
          
          {isLoading && (
            <div className="flex gap-3 justify-start">
              <div className="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center flex-shrink-0">
                <Bot className="w-4 h-4 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg px-4 py-2">
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm">Thinking...</span>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Quick Questions */}
        {messages.length === 0 && context?.currentSupplement && (
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Quick questions:
            </p>
            <div className="flex flex-wrap gap-2">
              {quickQuestions.slice(0, 4).map((question, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickQuestion(question)}
                  disabled={disabled || isLoading}
                  className="text-xs"
                >
                  {question}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Input Area */}
        <div className="flex gap-2">
          <Textarea
            ref={textareaRef}
            placeholder={
              disabled 
                ? "Connect to start chatting..." 
                : context?.currentSupplement
                  ? `Ask about ${context.currentSupplement}...`
                  : "Ask me anything about supplements..."
            }
            value={inputMessage}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={disabled || isLoading}
            className="flex-1 min-h-[60px] max-h-[120px] resize-none"
            rows={2}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || disabled || isLoading}
            className="self-end"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
