import React, { createContext, useContext, useState } from 'react';
import { clsx } from 'clsx';

interface TabsContextType {
  value: string;
  onValueChange: (value: string) => void;
}

const TabsContext = createContext<TabsContextType | undefined>(undefined);

const useTabsContext = () => {
  const context = useContext(TabsContext);
  if (!context) {
    throw new Error('Tabs components must be used within a Tabs provider');
  }
  return context;
};

interface TabsProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onValueChange'> {
  value?: string;
  onValueChange?: (value: string) => void;
  defaultValue?: string;
}

export const Tabs: React.FC<TabsProps> = ({
  children,
  value: controlledValue,
  onValueChange,
  defaultValue = '',
  className,
  ...props
}) => {
  const [internalValue, setInternalValue] = useState(defaultValue);
  const value = controlledValue ?? internalValue;
  const handleValueChange = onValueChange ?? setInternalValue;

  return (
    <TabsContext.Provider value={{ value, onValueChange: handleValueChange }}>
      <div className={clsx('tabs', className)} {...props}>
        {children}
      </div>
    </TabsContext.Provider>
  );
};

interface TabsContentProps extends React.HTMLAttributes<HTMLDivElement> {
  value: string;
}

export const TabsContent: React.FC<TabsContentProps> = ({
  children,
  value: tabValue,
  className,
  ...props
}) => {
  const { value } = useTabsContext();

  if (value !== tabValue) {
    return null;
  }

  return (
    <div className={clsx('tabs-content', className)} {...props}>
      {children}
    </div>
  );
};

export const TabsList: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({
  children,
  className,
  ...props
}) => (
  <div
    role="tablist"
    className={clsx('tabs-list flex space-x-1 rounded-lg bg-gray-100 p-1', className)}
    {...props}
  >
    {children}
  </div>
);

interface TabsTriggerProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  value: string;
}

export const TabsTrigger: React.FC<TabsTriggerProps> = ({
  children,
  value: tabValue,
  className,
  ...props
}) => {
  const { value, onValueChange } = useTabsContext();
  const isActive = value === tabValue;

  return (
    <button
      role="tab"
      aria-selected={isActive}
      className={clsx(
        'tabs-trigger px-3 py-2 text-sm font-medium rounded-md transition-colors',
        isActive
          ? 'bg-white text-gray-900 shadow-sm'
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50',
        className
      )}
      onClick={() => onValueChange(tabValue)}
      {...props}
    >
      {children}
    </button>
  );
};