import React from 'react';
import { motion, MotionProps } from 'framer-motion';
import { clsx } from 'clsx';

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
  clickable?: boolean;
  animate?: boolean;
  motionProps?: MotionProps;
}

const variantClasses = {
  default: 'card',
  elevated: 'card shadow-lg',
  outlined: 'card border-2',
  filled: 'bg-gray-50 border border-gray-200 rounded-lg',
};

const paddingClasses = {
  none: '',
  sm: 'p-3',
  md: 'p-4',
  lg: 'p-6',
  xl: 'p-8',
};

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = 'md',
  hover = false,
  clickable = false,
  animate = false,
  motionProps,
  className,
  onClick,
  ...props
}) => {
  const cardClasses = clsx(
    variantClasses[variant],
    paddingClasses[padding],
    {
      'card-hover': hover,
      'cursor-pointer': clickable || onClick,
      'transition-transform hover:scale-105': clickable && !animate,
    },
    className
  );

  if (animate) {
    return (
      <motion.div
        className={cardClasses}
        onClick={onClick}
        whileHover={
          hover || clickable
            ? { scale: 1.02, boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)' }
            : undefined
        }
        whileTap={clickable ? { scale: 0.98 } : undefined}
        transition={{ type: 'spring', stiffness: 400, damping: 17 }}
        {...(motionProps || {})}
        {...(props as any)}
      >
        {children}
      </motion.div>
    );
  }

  return (
    <div className={cardClasses} onClick={onClick} {...props}>
      {children}
    </div>
  );
};

export default Card;
