{"version": 3, "file": "withLatestFrom.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/withLatestFrom.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AACA,qCAAuC;AACvC,2DAAgE;AAChE,qDAAoD;AACpD,6CAA4C;AAC5C,qCAAoC;AACpC,qCAAiD;AAmDjD,SAAgB,cAAc;IAAO,gBAAgB;SAAhB,UAAgB,EAAhB,qBAAgB,EAAhB,IAAgB;QAAhB,2BAAgB;;IACnD,IAAM,OAAO,GAAG,wBAAiB,CAAC,MAAM,CAAwC,CAAC;IAEjF,OAAO,cAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;QAC1B,IAAM,WAAW,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QAInC,IAAI,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,cAAM,OAAA,KAAK,EAAL,CAAK,CAAC,CAAC;QAGvC,IAAI,KAAK,GAAG,KAAK,CAAC;gCAMT,CAAC;YACR,qBAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAC5B,6CAAwB,CACtB,UAAU,EACV,UAAC,KAAK;gBACJ,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;gBACvB,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;oBAE1B,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;oBAKnB,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,mBAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAK,CAAC,CAAC;iBAC1D;YACH,CAAC,EAGD,WAAI,CACL,CACF,CAAC;;QApBJ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;oBAAnB,CAAC;SAqBT;QAGD,MAAM,CAAC,SAAS,CACd,6CAAwB,CAAC,UAAU,EAAE,UAAC,KAAK;YACzC,IAAI,KAAK,EAAE;gBAET,IAAM,MAAM,kBAAI,KAAK,UAAK,WAAW,EAAC,CAAC;gBACvC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,wCAAI,MAAM,IAAE,CAAC,CAAC,MAAM,CAAC,CAAC;aACxD;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AApDD,wCAoDC"}