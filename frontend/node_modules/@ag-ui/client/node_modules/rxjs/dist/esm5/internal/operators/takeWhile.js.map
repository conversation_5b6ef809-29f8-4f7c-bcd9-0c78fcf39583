{"version": 3, "file": "takeWhile.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/takeWhile.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAC;AAoDhE,MAAM,UAAU,SAAS,CAAI,SAA+C,EAAE,SAAiB;IAAjB,0BAAA,EAAA,iBAAiB;IAC7F,OAAO,OAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,CAAC,SAAS,CACd,wBAAwB,CAAC,UAAU,EAAE,UAAC,KAAK;YACzC,IAAM,MAAM,GAAG,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;YACzC,CAAC,MAAM,IAAI,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,CAAC,MAAM,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;QACnC,CAAC,CAAC,CACH,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC"}